import { axiosInstance } from '@/shared/lib/api.client';
import { Model, CreateModelData, UpdateModelData } from '@/shared/types/model.schema';

// Tipos para a API
// A resposta da API deve corresponder diretamente ao schema Zod
export type ModelApiResponse = Model;

export interface ModelListApiResponse {
  models: ModelApiResponse[];
  total_count: number;
}

export interface ToggleFavoritePayload {
  is_favorite: boolean;
}

// API para Modelos
export const modelsApi = {
  // Buscar todos os modelos
  getAll: async (
    params?: { page?: number; page_size?: number }
  ): Promise<ModelListApiResponse> => {
    try {
      const query = params
        ? `?${Object.entries(params)
            .filter(([_, v]) => v !== undefined)
            .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(String(v))}`)
            .join('&')}`
        : '';
      const response = await axiosInstance.get(`/protected/models${query}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar modelos:', error);
      throw error;
    }
  },

  // Buscar modelos por categoria
  getByCategory: async (category: string): Promise<ModelApiResponse[]> => {
    try {
      const response = await axiosInstance.get(`/protected/models?category=${category}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar modelos da categoria ${category}:`, error);
      throw error;
    }
  },

  // Buscar modelos favoritos
  getFavorites: async (): Promise<ModelApiResponse[]> => {
    try {
      const response = await axiosInstance.get('/protected/models?favorites_only=true');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar modelos favoritos:', error);
      throw error;
    }
  },

  // Buscar modelos personalizados
  getCustom: async (): Promise<ModelApiResponse[]> => {
    try {
      const response = await axiosInstance.get('/protected/models?custom_only=true');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar modelos personalizados:', error);
      throw error;
    }
  },

  // Buscar modelo por ID
  getById: async (id: string): Promise<ModelApiResponse> => {
    try {
      const response = await axiosInstance.get(`/protected/models/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar modelo ${id}:`, error);
      throw error;
    }
  },

  // Criar novo modelo
  create: async (payload: CreateModelData): Promise<ModelApiResponse> => {
    try {
      const response = await axiosInstance.post('/protected/models', payload);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar modelo:', error);
      throw error;
    }
  },

  // Atualizar modelo existente
  update: async (id: string, payload: UpdateModelData): Promise<ModelApiResponse> => {
    try {
      const response = await axiosInstance.put(`/protected/models/${id}`, payload);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar modelo ${id}:`, error);
      throw error;
    }
  },

  // Excluir modelo
  delete: async (id: string): Promise<void> => {
    try {
      await axiosInstance.delete(`/protected/models/${id}`);
    } catch (error) {
      console.error(`Erro ao excluir modelo ${id}:`, error);
      throw error;
    }
  },

  // Marcar/desmarcar como favorito
  toggleFavorite: async (id: string, isFavorite: boolean): Promise<ModelApiResponse> => {
    try {
      const payload: ToggleFavoritePayload = { is_favorite: isFavorite };
      const response = await axiosInstance.put(`/protected/models/${id}/favorite`, payload);
      return response.data;
    } catch (error) {
      console.error(`Erro ao ${isFavorite ? 'favoritar' : 'desfavoritar'} modelo ${id}:`, error);
      throw error;
    }
  },

  // Atualizar data de último uso
  updateLastUsed: async (id: string): Promise<ModelApiResponse> => {
    try {
      const response = await axiosInstance.put(`/protected/models/${id}/last-used`, {});
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar data de último uso do modelo ${id}:`, error);
      throw error;
    }
  },

  // Duplicar modelo
  duplicate: async (id: string): Promise<ModelApiResponse> => {
    try {
      const response = await axiosInstance.post(`/protected/models/${id}/duplicate`, {});
      return response.data;
    } catch (error) {
      console.error(`Erro ao duplicar modelo ${id}:`, error);
      throw error;
    }
  }
};
