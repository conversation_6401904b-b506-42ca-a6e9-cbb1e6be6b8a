import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/shared/hooks/use-toast';
import { modelsApi } from '@/shared/api/models.api';
import { z } from 'zod';
import {
  Model,
  CreateModelData,
  UpdateModelData,
  modelSchema,
} from '@/shared/types/model.schema';
import { MODELS_QUERY_KEYS } from '@/shared/hooks/constants';
import { useNavigate } from '@tanstack/react-router';

export function useModelMutations() {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const navigate = useNavigate();

    const invalidateModelsCache = () => {
        queryClient.invalidateQueries({ queryKey: MODELS_QUERY_KEYS.all });
    };

    const createModelMutation = useMutation({
        mutationFn: (data: CreateModelData) => modelsApi.create(data),
        onSuccess: (data) => {
            invalidateModelsCache();
            toast({ title: 'Modelo criado', description: `O modelo "${data.title}" foi criado com sucesso.` });
        },
        onError: (error) => {
            toast({ title: 'Erro ao criar modelo', description: error.message, variant: 'destructive' });
        },
    });

    const updateModelMutation = useMutation({
        mutationFn: ({ id, data }: { id: string; data: UpdateModelData }) => modelsApi.update(id, data),
        onSuccess: (data) => {
            invalidateModelsCache();
            queryClient.invalidateQueries({ queryKey: MODELS_QUERY_KEYS.detail(data.id) });
            toast({ title: 'Modelo atualizado', description: `O modelo "${data.title}" foi atualizado.` });
        },
        onError: (error) => {
            toast({ title: 'Erro ao atualizar modelo', description: error.message, variant: 'destructive' });
        },
    });

    const deleteModelMutation = useMutation({
        mutationFn: (id: string) => modelsApi.delete(id),
        onSuccess: (_, id) => {
            invalidateModelsCache();
            queryClient.removeQueries({ queryKey: MODELS_QUERY_KEYS.detail(id) });
            toast({ title: 'Modelo excluído', description: 'O modelo foi excluído com sucesso.' });
            navigate({ to: '/models' });
        },
        onError: (error) => {
            toast({ title: 'Erro ao excluir modelo', description: error.message, variant: 'destructive' });
        },
    });

    const toggleFavoriteMutation = useMutation({
        mutationFn: ({ id, isFavorite }: { id: string; isFavorite: boolean }) =>
            modelsApi.toggleFavorite(id, isFavorite),
        onSuccess: (data) => {
            invalidateModelsCache();
            queryClient.setQueryData(MODELS_QUERY_KEYS.detail(data.id), data);
            toast({
                title: data.isFavorite ? 'Modelo favoritado' : 'Modelo desfavoritado',
            });
        },
        onError: (error) => {
            toast({ title: 'Erro ao favoritar', description: error.message, variant: 'destructive' });
        },
    });

    const duplicateModelMutation = useMutation({
        mutationFn: (id: string) => modelsApi.duplicate(id),
        onSuccess: (newModel) => {
            invalidateModelsCache();
            toast({ title: "Modelo duplicado", description: `Novo modelo "${newModel.title}" criado.` });
            navigate({ to: '/models/$modelId', params: { modelId: newModel.id } });
        },
        onError: (error: any) => {
            toast({ title: "Erro ao duplicar", description: error.message, variant: "destructive" });
        },
    });

    return {
        createModel: createModelMutation.mutate,
        updateModel: updateModelMutation.mutate,
        deleteModel: deleteModelMutation.mutate,
        toggleFavorite: toggleFavoriteMutation.mutate,
        duplicateModel: duplicateModelMutation.mutate,
        isCreating: createModelMutation.isPending,
        isUpdating: updateModelMutation.isPending,
        isDeleting: deleteModelMutation.isPending,
        isTogglingFavorite: toggleFavoriteMutation.isPending,
        isDuplicating: duplicateModelMutation.isPending,
    };
} 