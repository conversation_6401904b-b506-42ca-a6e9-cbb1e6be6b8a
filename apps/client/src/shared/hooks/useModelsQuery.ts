import { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/shared/hooks/use-toast';
import { modelsApi } from '@/shared/api/models.api';
import { z } from 'zod';
import { Model, modelSchema } from '@/shared/types/model.schema';
import { MODELS_QUERY_KEYS } from './constants';

export function useModelsQuery(options?: {
  initialPage?: number;
  initialPageSize?: number;
  initialModelId?: string;
  initialCategory?: string;
}) {
  const { toast } = useToast();
  const [currentModelId, setCurrentModelId] = useState<string | undefined>(
    options?.initialModelId
  );
  const [currentCategory, setCurrentCategory] = useState<string | undefined>(
    options?.initialCategory
  );
  const [page, setPage] = useState<number>(options?.initialPage ?? 0);
  const [pageSize, setPageSize] = useState<number>(options?.initialPageSize ?? 20);

  const modelsQuery = useQuery({
    queryKey: [...MODELS_QUERY_KEYS.all, { page, pageSize }],
    queryFn: async (): Promise<{ models: Model[]; totalCount: number }> => {
      try {
        const response = await modelsApi.getAll({ page, page_size: pageSize });
        const parsedData = z.array(modelSchema).safeParse(response.models);

        if (!parsedData.success) {
          console.error("Zod validation error (modelsQuery):", parsedData.error);
          throw new Error("Invalid data from API (model list).");
        }

        return { models: parsedData.data, totalCount: response.total_count };
      } catch (error) {
        toast({
          title: "Error loading models",
          description: error instanceof Error ? error.message : "Could not load model list",
          variant: "destructive"
        });
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const modelDetailsQuery = useQuery({
    queryKey: MODELS_QUERY_KEYS.detail(currentModelId || ''),
    queryFn: async (): Promise<Model | null> => {
      if (!currentModelId) return null;
      try {
        const model = await modelsApi.getById(currentModelId);
        const parsedData = modelSchema.safeParse(model);

        if (!parsedData.success) {
          console.error("Zod validation error (modelDetailsQuery):", parsedData.error);
          throw new Error("Invalid data from API (model details).");
        }

        return parsedData.data;
      } catch (error) {
        toast({
          title: "Error loading model",
          description: error instanceof Error ? error.message : "Could not load model details",
          variant: "destructive"
        });
        throw error;
      }
    },
    enabled: !!currentModelId,
  });

  const categoryModelsQuery = useQuery({
    queryKey: MODELS_QUERY_KEYS.category(currentCategory || ''),
    queryFn: async (): Promise<Model[]> => {
      if (!currentCategory) return [];
      try {
        const models = await modelsApi.getByCategory(currentCategory);
        const parsedData = z.array(modelSchema).safeParse(models);

        if (!parsedData.success) {
          console.error("Zod validation error (categoryModelsQuery):", parsedData.error);
          throw new Error("Invalid data from API (category models).");
        }

        return parsedData.data;
      } catch (error) {
        toast({
          title: "Error loading models",
          description: error instanceof Error ? error.message : "Could not load model list for the category",
          variant: "destructive"
        });
        throw error;
      }
    },
    enabled: !!currentCategory,
  });

  const getRecentModels = useMemo(() => {
    return [...(modelsQuery.data?.models || [])]
      .sort((a, b) => {
        const dateA = a.lastUsed ? new Date(a.lastUsed).getTime() : 0;
        const dateB = b.lastUsed ? new Date(b.lastUsed).getTime() : 0;
        return dateB - dateA;
      })
      .slice(0, 5);
  }, [modelsQuery.data?.models]);

  return {
    models: modelsQuery.data?.models || [],
    totalCount: modelsQuery.data?.totalCount ?? 0,
    page,
    pageSize,
    setPage,
    setPageSize,
    currentModel: modelDetailsQuery.data,
    categoryModels: categoryModelsQuery.data || [],
    recentModels: getRecentModels,

    isLoading: modelsQuery.isLoading,
    isLoadingDetails: modelDetailsQuery.isLoading,

    refetch: modelsQuery.refetch,
    setCurrentModelId,
    setCurrentCategory,

    getModelsByCategory: (category: string) =>
      modelsQuery.data?.models?.filter(m => m.category === category) || [],
    getFavoriteModels: () =>
      modelsQuery.data?.models?.filter(m => m.isFavorite) || [],
    getCustomModels: () =>
      modelsQuery.data?.models?.filter(m => m.type === "custom") || [],
  };
}