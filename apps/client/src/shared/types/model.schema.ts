import { z } from 'zod';

// Schemas para campos dinâmicos
export const selectOptionSchema = z.object({
  value: z.string(),
  label: z.string(),
});

export const fieldValidationSchema = z.object({
  required: z.boolean().optional(),
  minLength: z.number().optional(),
  maxLength: z.number().optional(),
  pattern: z.string().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  email: z.boolean().optional(),
  url: z.boolean().optional(),
});

export const conditionalRuleSchema = z.object({
  fieldId: z.string(),
  operator: z.enum(['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than']),
  value: z.any(),
});

export const dynamicFieldSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'textarea', 'select', 'radio', 'checkbox', 'date', 'number']),
  label: z.string(),
  placeholder: z.string().optional(),
  required: z.boolean(),
  validation: fieldValidationSchema.optional(),
  options: z.array(selectOptionSchema).optional(),
  conditional: conditionalRuleSchema.optional(),
});

export const formSectionSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  fields: z.array(dynamicFieldSchema),
  conditional: conditionalRuleSchema.optional(),
});

export const formSchemaSchema = z.object({
  sections: z.array(formSectionSchema),
  validation: z.record(fieldValidationSchema),
  conditional: z.array(conditionalRuleSchema),
});

export const templateContentSchema = z.object({
  content: z.any(), // TipTap JSON
  placeholders: z.array(z.object({
    id: z.string(),
    fieldId: z.string(),
    label: z.string(),
    transform: z.enum(['uppercase', 'lowercase', 'capitalize', 'date-format']).optional(),
    fallback: z.string().optional(),
  })),
});

export const outputTypesSchema = z.array(z.enum(['note', 'pdf', 'docx']));

// Schema para validação de modelos (estendido)
export const modelSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1, "O título é obrigatório"),
  category: z.string().min(1, "A categoria é obrigatória"),
  type: z.union([z.literal("system"), z.literal("custom")]),
  description: z.string().nullable().optional(),
  content: z.string().min(1, "O conteúdo é obrigatório"),
  isFavorite: z.boolean(),
  tags: z.array(z.string()),
  createdAt: z.string(),
  updatedAt: z.string(),
  lastUsed: z.string().nullable().optional(),

  // NOVOS CAMPOS PARA MODELOS DINÂMICOS
  templateContent: templateContentSchema.nullable().optional(),
  formSchema: formSchemaSchema.nullable().optional(),
  outputTypes: outputTypesSchema.nullable().optional(),
  usageCount: z.number().nullable().optional(),
});

// Schema para criação de modelos (estendido)
export const createModelSchema = z.object({
  title: z.string().min(1, "O título é obrigatório"),
  category: z.string().min(1, "A categoria é obrigatória"),
  description: z.string().optional(),
  content: z.string().min(1, "O conteúdo é obrigatório"),
  tags: z.array(z.string()).optional(),

  // NOVOS CAMPOS PARA MODELOS DINÂMICOS
  templateContent: templateContentSchema.optional(),
  formSchema: formSchemaSchema.optional(),
  outputTypes: outputTypesSchema.optional(),
});

// Schema para atualização de modelos (estendido)
export const updateModelSchema = z.object({
  title: z.string().min(1, "O título é obrigatório").optional(),
  category: z.string().min(1, "A categoria é obrigatória").optional(),
  description: z.string().optional(),
  content: z.string().min(1, "O conteúdo é obrigatório").optional(),
  tags: z.array(z.string()).optional(),
  isFavorite: z.boolean().optional(),

  // NOVOS CAMPOS PARA MODELOS DINÂMICOS
  templateContent: templateContentSchema.nullable().optional(),
  formSchema: formSchemaSchema.nullable().optional(),
  outputTypes: outputTypesSchema.nullable().optional(),
});

// Schemas para aplicação de modelos
export const formResponseSchema = z.object({
  fieldId: z.string(),
  value: z.any(),
});

export const applyModelRequestSchema = z.object({
  patientId: z.string().uuid(),
  appointmentId: z.string().uuid().optional(),
  formResponses: z.array(formResponseSchema),
  outputType: z.enum(['note', 'pdf', 'docx']),
});

export const applyModelResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  outputId: z.string().uuid().optional(),
  downloadUrl: z.string().optional(),
});

// Tipos derivados dos schemas
export type Model = z.infer<typeof modelSchema>;
export type CreateModelData = z.infer<typeof createModelSchema>;
export type UpdateModelData = z.infer<typeof updateModelSchema>;

// Tipos para campos dinâmicos
export type SelectOption = z.infer<typeof selectOptionSchema>;
export type FieldValidation = z.infer<typeof fieldValidationSchema>;
export type ConditionalRule = z.infer<typeof conditionalRuleSchema>;
export type DynamicField = z.infer<typeof dynamicFieldSchema>;
export type FormSection = z.infer<typeof formSectionSchema>;
export type FormSchema = z.infer<typeof formSchemaSchema>;
export type TemplateContent = z.infer<typeof templateContentSchema>;
export type OutputTypes = z.infer<typeof outputTypesSchema>;

// Tipos para aplicação de modelos
export type FormResponse = z.infer<typeof formResponseSchema>;
export type ApplyModelRequest = z.infer<typeof applyModelRequestSchema>;
export type ApplyModelResponse = z.infer<typeof applyModelResponseSchema>;

// Tipo para modelo dinâmico completo (usado no frontend)
export type DynamicModel = Model & {
  template: TemplateContent;
  form: FormSchema;
  outputs: {
    note: boolean;
    pdf: boolean;
    docx: boolean;
  };
};
