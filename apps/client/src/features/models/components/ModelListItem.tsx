import React from 'react';
import { cn } from "@/shared/lib/utils";
import { Badge } from "@/shared/ui/badge";
import { Button } from "@/shared/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/shared/ui/dropdown-menu";
import { FileText, FileBarChart, FileCog, Clock, Tag, Star, SlidersHorizontal, FileEdit, Copy, Trash2, Edit } from "lucide-react";
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Model } from "@/shared/types/model.schema"; // Importar tipo Model

import { useState } from "react";
import { ApplyModelContextDialog } from "@/features/models/components/ApplyModelContextDialog";
import { PlaceholderContext } from "@/shared/lib/placeholder-engine";
import { ConfirmDeleteDialog } from "@/shared/components/ConfirmDeleteDialog";

// Props esperadas pelo componente
interface ModelListItemProps {
  model: Model;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onToggleFavorite: (id: string, isFavorite: boolean) => void;
  onUseModel: (model: Model, e?: React.MouseEvent, context?: PlaceholderContext) => void;
  onDuplicateModel: (id: string, e?: React.MouseEvent) => void;
  onStartEditing: (id: string) => void;
  onDeleteModel?: (id: string) => void;
  isDeleting?: boolean;
}

// Funções auxiliares (podem ser movidas para utils se usadas em mais lugares)
const getCategoryIcon = (categoryId: string) => {
  switch (categoryId) {
    case "report": return <FileText className="h-3.5 w-3.5" />;
    case "evaluation": return <FileBarChart className="h-3.5 w-3.5" />;
    case "form": return <FileCog className="h-3.5 w-3.5" />;
    default: return <FileText className="h-3.5 w-3.5" />;
  }
};

const getCategoryName = (categoryId: string) => {
  // Idealmente, isso viria de uma fonte compartilhada
  const categoriesMap: { [key: string]: string } = {
    report: "Relatório",
    evaluation: "Avaliação",
    form: "Formulário",
    letter: "Carta",
    exercise: "Exercício",
    other: "Outro",
    document: "Documento", // Adicionado para cobrir casos
    therapy: "Plano Terapêutico" // Adicionado para cobrir casos
  };
  return categoriesMap[categoryId] || "Outro";
};


export function ModelListItem({
  model,
  isSelected,
  onSelect,
  onToggleFavorite,
  onUseModel,
  onDuplicateModel,
  onStartEditing,
  onDeleteModel,
  isDeleting = false,
}: ModelListItemProps) {
  const [isContextDialogOpen, setIsContextDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleUseClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsContextDialogOpen(true);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (onDeleteModel) {
      onDeleteModel(model.id);
    }
    setIsDeleteDialogOpen(false);
  };

  const handleApplyModel = (processedContent: string, context: PlaceholderContext, modelTitle: string, shouldAppend: boolean) => {
    // Não fechar o diálogo aqui, pois o ApplyModelContextDialog já o fecha

    // Verificar se temos um paciente no contexto
    if (context.patient?.id) {
      // Chamar o callback original com o modelo e o contexto processado
      onUseModel(model, undefined, context);
    } else {
      // Se não tiver paciente, mostrar mensagem de erro
      console.error('Nenhum paciente selecionado no contexto');
    }
  };
  return (
    <>
      <div
        key={model.id}
        className={cn(
          "p-3 border rounded-md cursor-pointer hover:bg-accent/30 transition-colors flex items-center gap-4",
          isSelected && "border-primary bg-accent/50"
        )}
        onClick={() => onSelect(model.id)}
      >
        {/* Informações Principais */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center mb-1 gap-1">
            {getCategoryIcon(model.category)}
            <span className="ml-2 text-xs text-muted-foreground">{getCategoryName(model.category)}</span>
            {model.type === "system" && (
              <Badge variant="outline" className="ml-2 text-[10px] px-1 border-blue-400 text-blue-700">Sistema</Badge>
            )}
            {model.type === "custom" && (
              <Badge variant="secondary" className="ml-2 text-[10px] px-1">Personalizado</Badge>
            )}
          </div>
          <h3 className="font-medium text-foreground line-clamp-1">{model.title}</h3>
          <p className="text-xs text-muted-foreground mt-1 line-clamp-1">{model.description}</p>
        </div>

        {/* Ações */}
        <div className="flex items-center gap-1 ml-auto">
          <Button variant="ghost" size="icon" className="h-7 w-7" title="Favoritar" onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(model.id, !model.isFavorite);
          }}>
            <Star className={cn("h-4 w-4", model.isFavorite && "fill-yellow-400 text-yellow-400")} />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-7 w-7" onClick={(e) => e.stopPropagation()}>
                <SlidersHorizontal className="h-4 w-4" /> {/* Ícone de Ações */}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
              <DropdownMenuItem onClick={handleUseClick}>
                <FileEdit className="mr-2 h-4 w-4" /> Usar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => onDuplicateModel(model.id, e)}>
                <Copy className="mr-2 h-4 w-4" /> Duplicar
              </DropdownMenuItem>
              {model.type === "custom" && (
                <DropdownMenuItem onClick={() => onStartEditing(model.id)}>
                  <Edit className="mr-2 h-4 w-4" /> Editar
                </DropdownMenuItem>
              )}
              {model.type === "custom" && onDeleteModel && (
                <DropdownMenuItem
                  onClick={handleDeleteClick}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" /> Excluir
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Diálogo de aplicação de modelo com contexto */}
      <ApplyModelContextDialog
        isOpen={isContextDialogOpen}
        onClose={() => setIsContextDialogOpen(false)}
        model={model}
        onApply={handleApplyModel}
      />

      {/* Diálogo de confirmação de exclusão */}
      {onDeleteModel && (
        <ConfirmDeleteDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          title="Excluir Modelo"
          description="Tem certeza que deseja excluir o modelo"
          itemName={model.title}
          itemType="modelo"
          onConfirm={handleConfirmDelete}
          isDeleting={isDeleting}
        />
      )}
    </>
  );
}
