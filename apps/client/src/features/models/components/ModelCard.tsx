import { cn } from "@/shared/lib/utils";
import { formatDate } from "@/shared/lib/date";
import { But<PERSON> } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/shared/ui/card";
import { Star, Clock } from "lucide-react";
import { useState } from "react";
import { ApplyModelContextDialog } from "@/features/models/components/ApplyModelContextDialog";
import { Model } from "@/shared/types/model.schema";
import { PlaceholderContext } from "@/shared/lib/placeholder-engine";

interface ModelCardProps {
  model: Model;
  isSelected: boolean;
  onSelect: (modelId: string) => void;
  onToggleFavorite: (modelId: string, isFavorite: boolean) => void;
  onEdit?: (modelId: string) => void;
  onUse: (model: Model, e?: React.MouseEvent, context?: PlaceholderContext) => void;
  getCategoryIcon: (categoryId: string) => React.ReactNode;
  getCategoryName: (categoryId: string) => string;
}

export function ModelCard({
  model,
  isSelected,
  onSelect,
  onToggleFavorite,
  onEdit,
  onUse,
  getCategoryIcon,
  getCategoryName,
}: ModelCardProps) {
  const [isContextDialogOpen, setIsContextDialogOpen] = useState(false);

  const handleUseClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsContextDialogOpen(true);
  };

  const handleApplyModel = (processedContent: string, context: PlaceholderContext, modelTitle: string, shouldAppend: boolean) => {
    // Não fechar o diálogo aqui, pois o ApplyModelContextDialog já o fecha

    // Verificar se temos um paciente no contexto
    if (context.patient?.id) {
      // Chamar o callback original com o modelo e o contexto processado
      onUse(model, undefined, context);
    } else {
      // Se não tiver paciente, mostrar mensagem de erro
      console.error('Nenhum paciente selecionado no contexto');
    }
  };
  return (
    <Card
      className={cn(
        "cursor-pointer hover:border-primary/50 transition-colors",
        isSelected && "border-primary"
      )}
      onClick={() => onSelect(model.id)}
    >
      <CardHeader className="p-4 pb-2 flex flex-row items-start justify-between">
        <div>
          <div className="flex gap-1 mb-2">
            <Badge variant="outline" className="text-xs flex items-center gap-1">
              {getCategoryIcon(model.category)}
              <span>{getCategoryName(model.category)}</span>
            </Badge>
            {model.type === "system" && (
              <Badge variant="outline" className="text-xs border-blue-400 text-blue-700">
                Sistema
              </Badge>
            )}
          </div>
          <CardTitle className="text-base line-clamp-1">{model.title}</CardTitle>
        </div>
        <div className="flex gap-0.5">
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(model.id, !model.isFavorite);
          }}>
            <Star className={cn("h-4 w-4", model.isFavorite && "fill-yellow-400 text-yellow-400")} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-2">
        <p className="text-xs text-muted-foreground line-clamp-2">{model.description}</p>
      </CardContent>
      <CardFooter className="bg-muted/20 px-4 py-2 text-xs flex justify-between items-center border-t">
        <div className="flex items-center">
          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
          <span className="text-muted-foreground">{formatDate(model.createdAt)}</span>
        </div>
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" className="h-6 px-2 text-xs" onClick={(e) => {
            e.stopPropagation();
            onSelect(model.id);
          }}>
            Visualizar
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2 text-xs" onClick={handleUseClick}>
            Usar
          </Button>
          {model.type === "custom" && onEdit && (
            <Button variant="ghost" size="sm" className="h-6 px-2 text-xs" onClick={(e) => {
              e.stopPropagation();
              onEdit(model.id);
            }}>
              Editar
            </Button>
          )}
        </div>
      </CardFooter>

      {/* Diálogo de aplicação de modelo com contexto */}
      <ApplyModelContextDialog
        isOpen={isContextDialogOpen}
        onClose={() => setIsContextDialogOpen(false)}
        model={model}
        onApply={handleApplyModel}
      />
    </Card>
  );
}
