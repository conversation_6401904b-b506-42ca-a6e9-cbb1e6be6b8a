import React from 'react';
import { useState } from "react";
import { ApplyModelContextDialog } from "@/features/models/components/ApplyModelContextDialog";
import { PlaceholderContext } from "@/shared/lib/placeholder-engine";
import { useNavigate } from "@tanstack/react-router";
import {
  Star,
  Copy,
  Edit,
  FileText,
  Trash2,
  FileEdit,
  Tag,
  Clock,
  MoreVertical,
  Download
} from "lucide-react";
import { Button } from "@/shared/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/shared/ui/card";
import { Badge } from "@/shared/ui/badge";
import { cn } from "@/shared/lib/utils";
import { useModelsQuery } from "@/shared/hooks/useModelsQuery";
import { useModelMutations } from "@/shared/hooks/useModelMutations";
import { useToast } from "@/shared/hooks/use-toast";
import { Separator } from "@/shared/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/shared/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/shared/ui/alert-dialog";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { parseISO, isValid } from "date-fns";

interface ModelDetailProps {
  modelId: string;
  onEdit?: () => void;
}

export function ModelDetail({ modelId, onEdit }: ModelDetailProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isContextDialogOpen, setIsContextDialogOpen] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const {
    currentModel,
    isLoadingDetails,
  } = useModelsQuery({
    initialModelId: modelId,
  });

  const {
    toggleFavorite,
    deleteModel,
    duplicateModel,
  } = useModelMutations();

  if (isLoadingDetails || !currentModel) {
    return (
      <Card className="h-full">
        <CardContent className="p-6 h-full">
          <div className="animate-pulse space-y-4 w-full">
            <div className="h-6 bg-muted rounded w-1/3"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <Separator className="my-4" />
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded"></div>
              <div className="h-4 bg-muted rounded"></div>
              <div className="h-4 bg-muted rounded w-2/3"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleCopyToClipboard = () => {
    // Para conteúdo HTML, extrai apenas o texto para a área de transferência
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = currentModel.content;
    const plainText = tempDiv.textContent || tempDiv.innerText || currentModel.content;

    navigator.clipboard.writeText(plainText);
    toast({
      title: "Conteúdo copiado",
      description: "O conteúdo do modelo foi copiado para a área de transferência.",
    });
  };

  const handleDelete = () => {
    deleteModel(modelId);
    setIsDeleteDialogOpen(false);
  };

  const handleDuplicate = () => {
    if (!currentModel) return;
    duplicateModel(currentModel.id);
  };

  const handleUseModel = () => {
    setIsContextDialogOpen(true);
  };

  const handleApplyModel = (processedContent: string, context: PlaceholderContext, modelTitle: string) => {
    // Fechar o diálogo
    setIsContextDialogOpen(false);

    // Verificar se temos um paciente selecionado
    if (!context.patient) {
      toast({
        title: "Erro ao aplicar modelo",
        description: "Selecione um paciente para aplicar este modelo.",
        variant: "destructive"
      });
      return;
    }

    // Determinar o tipo de ação com base na categoria do modelo
    const modelCategory = currentModel.category || "document";

    // Obter o ID do paciente do contexto
    const patientId = context.patient.id;

    if (!patientId) {
      toast({
        title: "Erro ao aplicar modelo",
        description: "Não foi possível determinar o ID do paciente.",
        variant: "destructive"
      });
      return;
    }

    // Navegar para a rota apropriada com base na categoria do modelo
    if (modelCategory === "session_note") {
      // Se temos um ID de agendamento, passar para a rota
      const appointmentId = context.appointment?.id;

      // Navegar para a página de nova nota de sessão
      navigate({
        to: `/person/${patientId}/session-notes/new`,
        search: appointmentId ? { appointmentId } : {}
      });
    } else {
      // Para qualquer outro tipo, navegar para a nova rota de criação de documento a partir de modelo
      navigate({
        to: `/person/${patientId}/documents/new-from-model/${modelId}`
      });
    }

    toast({
      title: "Modelo aplicado",
      description: `O modelo "${modelTitle}" foi aplicado. Você foi redirecionado para o editor.`
    });
  };

  // Removida a função handleContextSelected que não é mais necessária

  // Formatar data
  const formatDate = (date: Date) => {
    if (!isValid(date)) return "Data inválida";
    return format(date, "dd/MM/yyyy", { locale: ptBR });
  };

  // Nome da categoria formatado
  const getCategoryName = (category: string) => {
    switch (category) {
      case "report":
        return "Relatório";
      case "form":
        return "Formulário";
      case "document":
        return "Documento";
      case "therapy":
        return "Plano Terapêutico";
      default:
        return category;
    }
  };
  // Exportar modelo como JSON
  const handleExportModel = () => {
    if (!currentModel) return;
    // Montar objeto para exportação (inclui todos os campos relevantes)
    const exportData = {
      title: currentModel.title,
      description: currentModel.description,
      category: currentModel.category,
      type: currentModel.type,
      content: currentModel.content,
      tags: currentModel.tags,
      created_at: currentModel.createdAt,
      updated_at: currentModel.updatedAt,
      last_used: currentModel.lastUsed,
      is_favorite: currentModel.isFavorite,
    };
    const jsonData = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `modelo-${currentModel.title.replace(/\s+/g, "_")}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast({
      title: "Modelo exportado",
      description: "O modelo foi exportado em formato JSON.",
    });
  };

  return (
    <>
      <Card className="h-full">
        <CardHeader className="pb-3 flex flex-row items-start justify-between space-y-0">
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{getCategoryName(currentModel.category)}</Badge>
              {currentModel.type === "custom" && <Badge variant="secondary">Personalizado</Badge>}
            </div>
            <CardTitle className="line-clamp-1">{currentModel.title}</CardTitle>
            <CardDescription className="line-clamp-2">{currentModel.description}</CardDescription>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => toggleFavorite({ id: modelId, isFavorite: !currentModel.isFavorite })}
              title={currentModel.isFavorite ? "Remover dos favoritos" : "Adicionar aos favoritos"}
            >
              <Star
                className={cn(
                  "h-5 w-5",
                  currentModel.isFavorite ? "fill-yellow-400 text-yellow-400" : ""
                )}
              />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleCopyToClipboard}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copiar conteúdo
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleUseModel}>
                  <FileEdit className="h-4 w-4 mr-2" />
                  Usar modelo
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDuplicate}>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExportModel()}>
                  <Download className="h-4 w-4 mr-2" />
                  Exportar
                </DropdownMenuItem>
                {currentModel.type === "custom" && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => setIsDeleteDialogOpen(true)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Excluir
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <Separator />

        <CardContent className="pt-4">
          <div className="mb-4 flex flex-wrap gap-2 items-center">
            <div className="flex items-center text-xs text-muted-foreground mr-3">
              <Clock className="h-3 w-3 mr-1" />
              <span>Criado: {formatDate(parseISO(currentModel.createdAt))}</span>
              {currentModel.lastUsed && (
                <span className="ml-1">(Último uso: {formatDate(parseISO(currentModel.lastUsed))})</span>
              )}
            </div>

            {currentModel.tags.length > 0 && (
              <div className="flex items-center flex-wrap gap-1">
                <Tag className="h-3 w-3 text-muted-foreground" />
                {currentModel.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Exibe o conteúdo como HTML renderizado */}
          <div className="bg-muted/20 rounded-md p-4 mb-3 prose prose-sm max-w-none max-h-[calc(100vh-350px)] overflow-auto">
            <div dangerouslySetInnerHTML={{ __html: currentModel.content }} />
          </div>
        </CardContent>

        <Separator className="mt-auto" />

        <CardFooter className="pt-4 flex justify-between">
          <Button variant="secondary" onClick={handleCopyToClipboard}>
            <Copy className="h-4 w-4 mr-2" />
            Copiar
          </Button>

          <div className="flex gap-2">
            {currentModel.type === "custom" && (
              <Button variant="outline" onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Editar
              </Button>
            )}
            <Button onClick={handleUseModel}>
              <FileEdit className="h-4 w-4 mr-2" />
              Usar Modelo
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Dialog de confirmação para exclusão */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir modelo</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este modelo? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Diálogo de aplicação de modelo com contexto */}
      <ApplyModelContextDialog
        isOpen={isContextDialogOpen}
        onClose={() => setIsContextDialogOpen(false)}
        model={currentModel}
        onApply={handleApplyModel}
      />
    </>
  );
}