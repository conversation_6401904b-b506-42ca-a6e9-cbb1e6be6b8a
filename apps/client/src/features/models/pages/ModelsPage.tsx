import { useState } from "react";
import { ApplyModelContextDialog } from "@/features/models/components/ApplyModelContextDialog";
import { ModelDocumentDialog } from "@/features/models/components/ModelDocumentDialog";
import { ModelApplicationWizard } from "@/features/models/components/ModelApplicationWizard";
import { PlaceholderContext } from "@/shared/lib/placeholder-engine";
import { ModelFilters } from "@/features/models/components/ModelFilters";
import {
  Search,
  Plus,
  Grid as GridIcon,
  List as ListIcon,
  Loader2,
  ArrowLeft,
  SlidersHorizontal,
} from "lucide-react";
import { useModelsQuery } from "@/shared/hooks/useModelsQuery";
import { useModelMutations } from "@/shared/hooks/useModelMutations";
import { Model } from "@/shared/types/model.schema";
import { useNavigate } from "@tanstack/react-router";
import { useToast } from "@/shared/hooks/use-toast";
import { ModelEditorWrapper } from "@/features/models/components/ModelEditorWrapper";
import { ConfirmDeleteDialog } from "@/shared/components/ConfirmDeleteDialog";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Tabs, TabsList, TabsTrigger } from "@/shared/ui/tabs";
import { ScrollArea } from "@/shared/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { ModelList } from "@/features/models/components/ModelList";

export function ModelsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [editingModelId, setEditingModelId] = useState<string | null>(null);
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("recent");
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [isContextDialogOpen, setIsContextDialogOpen] = useState(false);
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [modelToDelete, setModelToDelete] = useState<Model | null>(null);
  const [selectedContext, setSelectedContext] = useState<PlaceholderContext | null>(null);
  const [isWizardOpen, setIsWizardOpen] = useState(false);
  const [wizardModel, setWizardModel] = useState<Model | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const { models, isLoading } = useModelsQuery();

  const {
    deleteModel,
    duplicateModel,
    toggleFavorite,
    isDeleting,
    isDuplicating,
    isTogglingFavorite
  } = useModelMutations();

  const handleToggleFavorite = (id: string, isFavorite: boolean) => {
    toggleFavorite({ id, isFavorite });
  };

  const handleUseModel = (model: Model, e?: React.MouseEvent, context?: PlaceholderContext) => {
    if (e) e.stopPropagation();
    if (context) {
      if (!context.patient) {
        toast({ title: "Erro ao aplicar modelo", description: "Selecione um paciente.", variant: "destructive" });
        return;
      }
      const patientId = context.patient.id;
      if (!patientId) {
        toast({ title: "Erro ao aplicar modelo", description: "ID do paciente não encontrado.", variant: "destructive" });
        return;
      }
      if (model.category === "session_note") {
        navigate({ to: '/person/$patientId/session-notes/new', params: { patientId }, search: { appointmentId: context.appointment?.id } });
      } else {
        setSelectedModel(model);
        setSelectedContext(context);
        setIsFormDialogOpen(true);
      }
    } else {
      setSelectedModel(model);
      setIsContextDialogOpen(true);
    }
  };

  const handleFormSubmit = (title: string, generatedContent: string) => {
    if (!selectedContext?.patient?.id) return;
    navigate({
      to: '/person/$patientId/documents/new',
      params: { patientId: selectedContext.patient.id },
      state: { initialTitle: title, initialContent: generatedContent } as any,
    });
    toast({ title: "Documento gerado", description: `O documento "${title}" foi gerado.` });
  };

  const handleDeleteClick = (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (model) {
      setModelToDelete(model);
      setIsDeleteDialogOpen(true);
    }
  };
  
  const handleConfirmDelete = () => {
    if (modelToDelete) {
      deleteModel(modelToDelete.id);
      setIsDeleteDialogOpen(false);
      setModelToDelete(null);
    }
  };

  const handleCreateNew = () => { setIsCreatingNew(true); };
  const handleCancelEditing = () => {
    setEditingModelId(null);
    setIsCreatingNew(false);
  };
  const handleEditModel = (id: string) => { setEditingModelId(id); };

  // Funções do wizard
  const handleOpenWizard = (model: Model) => {
    setWizardModel(model);
    setIsWizardOpen(true);
  };

  const handleCloseWizard = () => {
    setIsWizardOpen(false);
    setWizardModel(null);
  };

  const handleWizardFinish = async (responses: unknown[], outputType: 'note' | 'pdf' | 'docx') => {
    if (!wizardModel) return;

    try {
      // Aqui você faria a chamada para a API apply_model
      // Por enquanto, apenas mostramos uma mensagem de sucesso
      toast({
        title: "Modelo aplicado com sucesso",
        description: `${wizardModel.title} foi aplicado como ${outputType}`,
      });

      handleCloseWizard();
    } catch (error) {
      toast({
        title: "Erro ao aplicar modelo",
        description: "Ocorreu um erro ao aplicar o modelo. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  const getFilteredModels = () => {
    let result = [...models];
    if (searchTerm) {
      result = result.filter(model =>
        model.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (model.description?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        model.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    if (activeFilter === "favorites") result = result.filter(model => model.isFavorite);
    else if (activeFilter === "custom") result = result.filter(model => model.type === "custom");
    if (selectedCategories.length > 0) result = result.filter(model => selectedCategories.includes(model.category));
    
    if (sortBy === "recent") result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    else if (sortBy === "name") result.sort((a, b) => a.title.localeCompare(b.title));
    else if (sortBy === "lastUsed") {
      result.sort((a, b) => {
        const lastUsedA = a.lastUsed ? new Date(a.lastUsed).getTime() : 0;
        const lastUsedB = b.lastUsed ? new Date(b.lastUsed).getTime() : 0;
        return lastUsedB - lastUsedA;
      });
    }
    return result;
  };
  
  const filteredModels = getFilteredModels();

  const renderContent = () => {
    if (isCreatingNew || editingModelId) {
      const modelToEdit = editingModelId ? models.find(m => m.id === editingModelId) : undefined;
      return (
        <ModelEditorWrapper
          model={modelToEdit}
          onCancel={handleCancelEditing}
          isNew={isCreatingNew}
        />
      );
    }

    return (
      <div className="flex flex-col h-full">
        <div className="border-b p-4">
          <ModelFilters activeFilter={activeFilter as "all" | "favorites" | "custom"} selectedCategories={selectedCategories} onFilterChange={setActiveFilter as (filter: "all" | "favorites" | "custom") => void} onCategoryToggle={setSelectedCategories as any} />
        </div>
        <div className="border-b p-4 flex items-center justify-between gap-4">
          <div className="relative flex-grow max-w-xs">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input type="search" placeholder="Buscar modelos..." value={searchTerm} onChange={(e) => { setSearchTerm(e.target.value); }} className="pl-9 h-8" />
          </div>
          <div className="flex items-center space-x-2 flex-shrink-0">
             <Button variant="ghost" size="icon" onClick={() => { setShowAdvancedFilters(!showAdvancedFilters); }} title="Filtros avançados" className="h-8 w-8"><SlidersHorizontal className="h-4 w-4" /></Button>
            <Tabs value={viewMode} onValueChange={(v) => { setViewMode(v as "grid" | "list"); }}>
              <TabsList className="h-8">
                <TabsTrigger value="grid" className="h-6 px-2"><GridIcon className="h-4 w-4" /></TabsTrigger>
                <TabsTrigger value="list" className="h-6 px-2"><ListIcon className="h-4 w-4" /></TabsTrigger>
              </TabsList>
            </Tabs>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="h-8 w-[150px] text-xs"><SelectValue placeholder="Ordenar por" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Mais Recentes</SelectItem>
                <SelectItem value="name">Nome (A-Z)</SelectItem>
                <SelectItem value="lastUsed">Último Uso</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleCreateNew} size="sm" className="h-8"><Plus className="h-4 w-4 mr-2" />Novo Modelo</Button>
          </div>
        </div>
        <ScrollArea className="flex-1">
          <ModelList
            models={filteredModels}
            isLoading={isLoading || isDeleting || isDuplicating || isTogglingFavorite}
            viewMode={viewMode}
            onToggleFavorite={handleToggleFavorite}
            onDelete={handleDeleteClick}
            onDuplicate={duplicateModel}
            onEdit={handleEditModel}
            onUse={handleUseModel}
            selectedModelId={selectedModelId}
            onSelect={setSelectedModelId}
            onOpenWizard={handleOpenWizard}
          />
        </ScrollArea>
      </div>
    );
  };

  return (
    <main className="container max-w-full h-[calc(100vh-var(--navbar-height))] flex flex-col p-0">
      {selectedModel && (
        <ApplyModelContextDialog
          isOpen={isContextDialogOpen}
          onClose={() => { setIsContextDialogOpen(false); }}
          model={selectedModel}
          onApply={(processedContent, context) => { handleUseModel(selectedModel, undefined, context); }}
        />
      )}
      {selectedModel && selectedContext && (
        <ModelDocumentDialog
          isOpen={isFormDialogOpen}
          onClose={() => { setIsFormDialogOpen(false); }}
          modelContent={selectedModel.content}
          contextData={selectedContext}
          initialTitle={`${selectedModel.title} - ${selectedContext.patient?.full_name || ''}`}
          onSubmit={handleFormSubmit}
        />
      )}
      <div className="flex-1 overflow-hidden">
        {renderContent()}
      </div>
      <ConfirmDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Excluir Modelo"
        description="Tem certeza que deseja excluir o modelo"
        itemName={modelToDelete?.title}
        onConfirm={handleConfirmDelete}
        isDeleting={isDeleting}
      />

      {/* Wizard de aplicação de modelos */}
      {wizardModel && (
        <ModelApplicationWizard
          isOpen={isWizardOpen}
          onClose={handleCloseWizard}
          model={wizardModel as any}
          onFinish={handleWizardFinish}
        />
      )}
    </main>
  );
}
