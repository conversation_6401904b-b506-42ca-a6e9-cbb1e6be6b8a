import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useModelsQuery } from '@/shared/hooks/useModelsQuery';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';
import { ModelFormViewer } from '@/features/models/components/ModelFormViewer';
import { useToast } from '@/shared/hooks/use-toast';
import { PlaceholderContext } from '@/shared/lib/placeholder-engine';
import { Loader2 } from 'lucide-react';
import { useDocumentMutations } from '@/features/documents/hooks/useDocumentMutations';

interface NewDocumentFromModelProps {
  patientId: string;
  modelId: string;
}

export function NewDocumentFromModel({ patientId, modelId }: NewDocumentFromModelProps) {
  const navigate = useNavigate();
  const { toast } = useToast();

  const {
    currentModel,
    isLoadingDetails: isLoadingModel,
    setCurrentModelId,
  } = useModelsQuery({
    initialModelId: modelId,
  });

  const {
    currentPatient,
    isLoadingDetails: isLoadingPatient,
  } = usePatientQuery({
    initialPatientId: patientId,
  });

  const { uploadDocument, isUploading } = useDocumentMutations();

  useEffect(() => {
    if (modelId) {
      setCurrentModelId(modelId);
    }
  }, [modelId, setCurrentModelId]);

  const contextData: PlaceholderContext = useMemo(() => {
    if (!currentPatient) return {};

    return {
      patient: {
        id: currentPatient.id,
        full_name: currentPatient.full_name,
        date_of_birth: currentPatient.date_of_birth || undefined,
        age: currentPatient.age,
        phone: currentPatient.phone || undefined,
        email: currentPatient.email || undefined,
        address: currentPatient.address || undefined,
        city: currentPatient.city || undefined,
        state: currentPatient.state || undefined,
        cpf: currentPatient.cpf || undefined,
        school: currentPatient.school || undefined,
        notes: currentPatient.notes || undefined,
        gender: currentPatient.gender || undefined,
        marital_status: currentPatient.marital_status || undefined,
        rg: currentPatient.rg || undefined,
        ethnicity: currentPatient.ethnicity || undefined,
        nationality: currentPatient.nationality || undefined,
        naturalness: currentPatient.naturalness || undefined,
        occupation: currentPatient.occupation || undefined,
      },
      system: {
        currentDate: new Date(),
      },
    };
  }, [currentPatient]);

  const handleSave = (title: string, content: string) => {
    if (!currentPatient) {
      toast({
        title: 'Erro',
        description: 'Paciente não encontrado.',
        variant: 'destructive',
      });
      return;
    }

    const file = new File([content], `${title}.html`, { type: 'text/html' });
    const metadata = { title, patient_id: patientId, model_id: modelId };
    const formData = new FormData();
    formData.append('file', file);
    formData.append('metadata', JSON.stringify(metadata));

    uploadDocument(formData, {
      onSuccess: () => {
        toast({
          title: 'Documento criado',
          description: `O documento "${title}" foi salvo com sucesso.`,
        });
        navigate({ to: '/person/$patientId', params: { patientId }, search: { tab: 'documents' } });
      },
    });
  };

  const handleCancel = () => {
    navigate({ to: '/person/$patientId', params: { patientId } });
  };

  if (isLoadingModel || isLoadingPatient || !currentModel || !currentPatient) {
    return (
      <div className="flex flex-col items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Carregando dados...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <h1 className="text-2xl font-bold mb-6">Criar Documento a partir de Modelo</h1>

      <ModelFormViewer
        modelContent={currentModel.content}
        contextData={contextData}
        initialTitle={`${currentModel.title} - ${currentPatient.full_name}`}
        onSubmit={handleSave}
        onCancel={handleCancel}
        isSaving={isUploading}
      />
    </div>
  );
}
