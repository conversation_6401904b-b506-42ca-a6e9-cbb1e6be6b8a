import { useState, useEffect } from 'react';
import { Button } from '@/shared/ui/button';
import { useRouterState } from '@tanstack/react-router';
import { Card, CardContent } from '@/shared/ui/card';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { RichTextEditor } from '@/features/models/components/RichTextEditor';
import { useRef } from 'react';
import { ModelSelectorDialog } from '@/features/models/components/ModelSelectorDialog';
import { ApplyModelContextDialog } from '@/features/models/components/ApplyModelContextDialog';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';
import { useToast } from '@/shared/hooks/use-toast';
import { Model } from '@/shared/types/model.schema';
import { formatDate } from './utils';

interface DocumentEditorProps {
  patientId: string; // ID do paciente para contexto
  documentId?: string; // ID do documento para edição (opcional)
  initialContent?: string;
  initialTitle?: string;
  onSave: (content: string, title: string) => void;
  onCancel?: () => void;
}

export function DocumentEditor({
  patientId,
  documentId,
  initialContent = '',
  initialTitle = '',
  onSave,
  onCancel
}: DocumentEditorProps) {
  // Obter o estado da rota para verificar se há conteúdo inicial
  const routerState = useRouterState();
  const routeState = routerState.location.state as { initialContent?: string; initialTitle?: string } | null;

  // Usar o conteúdo inicial da rota, se disponível, ou o passado via props
  const [content, setContent] = useState(routeState?.initialContent || initialContent);
  const [title, setTitle] = useState(routeState?.initialTitle || initialTitle);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);
  const [isContextDialogOpen, setIsContextDialogOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const { toast } = useToast();

  // Referência para o editor Tiptap
  const editorRef = useRef<any>(null);

  // Hooks para obter dados do contexto
  const { currentPatient: patient } = usePatientQuery({ initialPatientId: patientId });

  // Efeito para definir um título padrão se não houver título inicial
  useEffect(() => {
    if (!initialTitle && patient) {
      setTitle(`Documento - ${patient.full_name} - ${formatDate(new Date())}`);
    }
  }, [initialTitle, patient]);

  // Função para lidar com a seleção de um modelo
  const handleModelSelect = (model: Model) => {
    setSelectedModel(model);
    setIsModelSelectorOpen(false);
    setIsContextDialogOpen(true);
  };

  // Função para receber o conteúdo processado do modelo
  const handleApplyModelContent = (
    processedContent: string,
    _context: any,
    modelTitle: string,
    shouldAppend: boolean
  ) => {
    if (shouldAppend && editorRef.current?.editor) {
      // Inserir no final do conteúdo existente
      const currentLength = editorRef.current.editor.storage.characterCount || 0;
      editorRef.current.editor.commands.insertContentAt(currentLength, processedContent);
      setContent(editorRef.current.editor.getHTML());
    } else {
      // Sobrescrever conteúdo
      setContent(processedContent);
      if (editorRef.current?.editor) {
        editorRef.current.editor.commands.setContent(processedContent, true);
      }
    }

    // Se o título estiver vazio ou for o título padrão, usar o título do modelo
    if (!title || title === `Documento - ${patient?.full_name} - ${formatDate(new Date())}`) {
      setTitle(modelTitle);
    }

    toast({
      title: "Modelo aplicado",
      description: `O modelo "${modelTitle}" foi aplicado com sucesso.`
    });
  };

  // Função para salvar o documento
  const handleSave = () => {
    if (!title.trim()) {
      toast({
        title: "Título obrigatório",
        description: "Por favor, informe um título para o documento.",
        variant: "destructive",
      });
      return;
    }

    if (!content.trim()) {
      toast({
        title: "Conteúdo obrigatório",
        description: "Por favor, adicione conteúdo ao documento.",
        variant: "destructive",
      });
      return;
    }

    onSave(content, title);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">{documentId ? 'Editar Documento' : 'Novo Documento'}</h2>
        <div className="space-x-2">
          <Button variant="outline" onClick={onCancel}>Cancelar</Button>
          <Button onClick={() => setIsModelSelectorOpen(true)}>Usar Modelo</Button>
          <Button onClick={handleSave}>Salvar</Button>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="title">Título</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Título do documento"
        />
      </div>

      <Card>
        <CardContent className="p-0">
          <RichTextEditor
            value={content}
            onChange={setContent}
            placeholder="Digite o conteúdo do documento aqui..."
            onEditorReady={(editor) => {
              editorRef.current = editor;
            }}
          />
        </CardContent>
      </Card>

      {/* Seletor de Modelos */}
      <ModelSelectorDialog
        isOpen={isModelSelectorOpen}
        onClose={() => setIsModelSelectorOpen(false)}
        onSelect={handleModelSelect}
        filter={{ category: 'document' }} // Filtrar apenas modelos de documentos
      />

      {/* Diálogo de aplicação de modelo com contexto */}
      {selectedModel && (
        <ApplyModelContextDialog
          isOpen={isContextDialogOpen}
          onClose={() => setIsContextDialogOpen(false)}
          model={selectedModel}
          initialContext={{ patientId }}
          onApply={handleApplyModelContent}
        />
      )}
    </div>
  );
}
